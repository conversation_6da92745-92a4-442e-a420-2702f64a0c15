# CanSell Ultimate - Docker Deployment Guide

This guide explains how to run CanSell Ultimate using Docker containers.

## Prerequisites

- Docker Desktop or Docker Engine (20.10+)
- Docker Compose (2.0+)
- At least 4GB RAM available for Docker
- At least 10GB free disk space

## Quick Start

### 1. Clone and Setup
```bash
git clone <repository-url>
cd CanSell-Ultimate
make setup
```

### 2. Choose Your Environment

#### Development Environment (Recommended for development)
```bash
make dev
# or
./scripts/docker-run.sh dev
```

#### Production Environment
```bash
make prod
# or
./scripts/docker-run.sh prod
```

#### Infrastructure Only (for local app development)
```bash
make infra
# or
./scripts/docker-run.sh infra
```

## Available Commands

### Using Makefile (Recommended)
```bash
make dev        # Start development environment
make prod       # Start production environment
make infra      # Start infrastructure services only
make stop       # Stop all services
make clean      # Clean up Docker resources
make logs       # Show logs for all services
make status     # Check service status
make setup      # Initial setup
```

### Using Docker Run Script
```bash
./scripts/docker-run.sh dev              # Development mode
./scripts/docker-run.sh prod             # Production mode
./scripts/docker-run.sh infra            # Infrastructure only
./scripts/docker-run.sh stop             # Stop services
./scripts/docker-run.sh clean            # Cleanup
./scripts/docker-run.sh logs [service]   # Show logs
./scripts/docker-run.sh status           # Check status
```

## Service URLs

Once running, access these URLs:

### Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **API Documentation**: http://localhost:3001/api/docs

### Management UIs
- **OpenSearch Dashboards**: http://localhost:5601
- **Redpanda Console**: http://localhost:8080
- **MinIO Console**: http://localhost:9001 (admin/minioadmin123)
- **MailHog**: http://localhost:8025

### Database Connections
- **PostgreSQL**: localhost:5432 (user: dev, password: dev123)
- **MongoDB**: localhost:27017 (user: dev, password: dev123)
- **Redis**: localhost:6379 (password: dev123)
- **OpenSearch**: localhost:9200

## Environment Modes

### Development Mode
- Hot reload for both frontend and backend
- Source code mounted as volumes
- Debug ports exposed
- All infrastructure services included
- Optimized for development workflow

### Production Mode
- Optimized Docker images
- No source code mounting
- Production-ready configuration
- All services containerized
- Suitable for staging/production

### Infrastructure Mode
- Only infrastructure services (databases, queues, etc.)
- Applications run locally with `pnpm dev`
- Best for hybrid development
- Faster iteration on application code

## Configuration

### Environment Variables
Copy `.env.example` to `.env` and customize:

```bash
cp .env.example .env
```

Key variables:
- `NODE_ENV`: Environment mode
- `JWT_SECRET`: JWT signing secret
- `DATABASE_URL`: PostgreSQL connection
- `MONGODB_URL`: MongoDB connection
- `REDIS_URL`: Redis connection

### Docker Compose Files
- `docker-compose.yml`: Production configuration
- `docker-compose.dev.yml`: Development configuration

## Troubleshooting

### Common Issues

#### Port Conflicts
If ports are already in use:
```bash
# Check what's using the port
lsof -i :3000
lsof -i :3001

# Stop conflicting services or change ports in docker-compose files
```

#### Memory Issues
If containers are killed due to memory:
```bash
# Increase Docker memory limit in Docker Desktop settings
# Or reduce service memory limits in docker-compose files
```

#### Permission Issues
```bash
# Make scripts executable
chmod +x scripts/*.sh
chmod +x infra/scripts/*.sh
```

#### Database Connection Issues
```bash
# Check if databases are healthy
docker-compose ps
docker-compose logs postgres
docker-compose logs mongodb
```

### Debugging

#### View Logs
```bash
# All services
make logs

# Specific service
docker-compose logs -f frontend
docker-compose logs -f backend
```

#### Access Container Shell
```bash
# Frontend container
docker exec -it cansell-frontend-dev sh

# Backend container
docker exec -it cansell-backend-dev sh

# Database containers
docker exec -it cansell-postgres psql -U dev -d cansell
docker exec -it cansell-mongodb mongosh
```

#### Check Service Health
```bash
make status
# or
docker-compose ps
```

### Performance Optimization

#### For Development
- Use infrastructure-only mode for faster iteration
- Increase Docker memory allocation
- Use SSD storage for better I/O performance

#### For Production
- Adjust resource limits in docker-compose.yml
- Use external managed databases for better performance
- Implement proper monitoring and alerting

## Data Persistence

All data is persisted in Docker volumes:
- `postgres_data`: PostgreSQL data
- `mongodb_data`: MongoDB data
- `redis_data`: Redis data
- `opensearch_data`: OpenSearch indices
- `redpanda_data`: Kafka topics
- `minio_data`: File uploads

### Backup Data
```bash
# Backup PostgreSQL
docker exec cansell-postgres pg_dump -U dev cansell > backup.sql

# Backup MongoDB
docker exec cansell-mongodb mongodump --out /backup

# Backup volumes
docker run --rm -v postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz /data
```

## Scaling

### Horizontal Scaling
For production, consider:
- Load balancer for frontend
- Multiple backend instances
- External managed databases
- Redis cluster
- Kafka cluster

### Resource Limits
Adjust in docker-compose files:
```yaml
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
```

## Security

### Production Security
- Change default passwords
- Use proper SSL certificates
- Implement proper firewall rules
- Regular security updates
- Monitor for vulnerabilities

### Environment Variables
Never commit sensitive data:
- Use `.env` files (gitignored)
- Use Docker secrets for production
- Rotate secrets regularly

## Monitoring

### Health Checks
All services include health checks:
```bash
# Check health status
docker-compose ps
```

### Metrics
- Application metrics via Prometheus (planned)
- Infrastructure metrics via Docker stats
- Log aggregation via centralized logging (planned)

## Support

For issues:
1. Check this documentation
2. Review logs: `make logs`
3. Check service status: `make status`
4. Clean and restart: `make clean && make dev`
5. Create an issue with logs and configuration details
