# CanSell Ultimate Makefile

.PHONY: help dev prod infra stop clean logs status install

# Default target
help:
	@echo "CanSell Ultimate - Docker Commands"
	@echo "=================================="
	@echo ""
	@echo "Available commands:"
	@echo "  make dev        - Start development environment"
	@echo "  make prod       - Start production environment"
	@echo "  make infra      - Start infrastructure services only"
	@echo "  make stop       - Stop all services"
	@echo "  make clean      - Clean up Docker resources"
	@echo "  make logs       - Show logs for all services"
	@echo "  make status     - Check service status"
	@echo "  make install    - Install dependencies locally"
	@echo ""

# Install dependencies locally
install:
	@echo "📦 Installing dependencies..."
	pnpm install

# Start development environment
dev:
	@echo "🚀 Starting development environment..."
	./scripts/docker-run.sh dev

# Start production environment
prod:
	@echo "🚀 Starting production environment..."
	./scripts/docker-run.sh prod

# Start infrastructure services only
infra:
	@echo "🏗️ Starting infrastructure services..."
	./scripts/docker-run.sh infra

# Stop all services
stop:
	@echo "🛑 Stopping all services..."
	./scripts/docker-run.sh stop

# Clean up Docker resources
clean:
	@echo "🧹 Cleaning up Docker resources..."
	./scripts/docker-run.sh clean

# Show logs
logs:
	@echo "📋 Showing logs..."
	./scripts/docker-run.sh logs

# Check service status
status:
	@echo "📊 Checking service status..."
	./scripts/docker-run.sh status

# Quick development setup
setup: install
	@echo "⚙️ Setting up development environment..."
	@if [ ! -f .env ]; then cp .env.example .env; echo "📝 Created .env file"; fi
	@chmod +x scripts/*.sh
	@chmod +x infra/scripts/*.sh
	@echo "✅ Setup complete! Run 'make dev' to start development environment"
