version: '3.8'

services:
  # Frontend Development
  frontend-dev:
    build:
      context: .
      dockerfile: apps/frontend/Dockerfile.dev
    container_name: cansell-frontend-dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:3001/api
    volumes:
      - ./apps/frontend:/app/apps/frontend
      - ./packages:/app/packages
      - /app/node_modules
      - /app/apps/frontend/node_modules
      - /app/apps/frontend/.next
    depends_on:
      - backend-dev
    networks:
      - cansell-network
    restart: unless-stopped

  # Backend Development
  backend-dev:
    build:
      context: .
      dockerfile: apps/backend/Dockerfile.dev
    container_name: cansell-backend-dev
    ports:
      - "3001:3001"
      - "9229:9229"  # Debug port
    environment:
      - NODE_ENV=development
      - PORT=3001
      - DATABASE_URL=*************************************/cansell
      - AUTH_DATABASE_URL=*************************************/cansell_auth
      - LOCATION_DATABASE_URL=*************************************/cansell_location
      - MONGODB_URL=******************************************
      - REDIS_URL=redis://:dev123@redis:6379
      - OPENSEARCH_URL=http://opensearch:9200
      - KAFKA_BROKERS=redpanda:9092
      - S3_ENDPOINT=http://minio:9000
      - S3_ACCESS_KEY=minioadmin
      - S3_SECRET_KEY=minioadmin123
      - S3_BUCKET=cansell-uploads
      - JWT_SECRET=dev-jwt-secret-key
      - REFRESH_TOKEN_SECRET=dev-refresh-token-secret
      - FRONTEND_URL=http://localhost:3000
    volumes:
      - ./apps/backend:/app/apps/backend
      - ./packages:/app/packages
      - /app/node_modules
      - /app/apps/backend/node_modules
      - /app/apps/backend/dist
    depends_on:
      postgres:
        condition: service_healthy
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
      opensearch:
        condition: service_healthy
      redpanda:
        condition: service_healthy
    networks:
      - cansell-network
    restart: unless-stopped

  # PostgreSQL for auth, locations, transactions
  postgres:
    image: postgres:15-alpine
    container_name: cansell-postgres
    environment:
      POSTGRES_DB: cansell
      POSTGRES_USER: dev
      POSTGRES_PASSWORD: dev123
      POSTGRES_MULTIPLE_DATABASES: cansell_auth,cansell_location
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infra/scripts/init-postgres.sh:/docker-entrypoint-initdb.d/init-postgres.sh
    networks:
      - cansell-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dev -d cansell"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MongoDB for items, categories, templates
  mongodb:
    image: mongo:7
    container_name: cansell-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: dev
      MONGO_INITDB_ROOT_PASSWORD: dev123
      MONGO_INITDB_DATABASE: cansell
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./infra/scripts/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js
    networks:
      - cansell-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching, sessions, queues
  redis:
    image: redis:7-alpine
    container_name: cansell-redis
    command: redis-server --appendonly yes --requirepass dev123
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - cansell-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # OpenSearch for search and analytics
  opensearch:
    image: opensearchproject/opensearch:2.11.0
    container_name: cansell-opensearch
    environment:
      - cluster.name=cansell-cluster
      - node.name=cansell-node
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m"
      - "DISABLE_INSTALL_DEMO_CONFIG=true"
      - "DISABLE_SECURITY_PLUGIN=true"
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    ports:
      - "9200:9200"
      - "9600:9600"
    volumes:
      - opensearch_data:/usr/share/opensearch/data
    networks:
      - cansell-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # OpenSearch Dashboards for search analytics
  opensearch-dashboards:
    image: opensearchproject/opensearch-dashboards:2.11.0
    container_name: cansell-dashboards
    environment:
      - 'OPENSEARCH_HOSTS=["http://opensearch:9200"]'
      - "DISABLE_SECURITY_DASHBOARDS_PLUGIN=true"
    ports:
      - "5601:5601"
    depends_on:
      opensearch:
        condition: service_healthy
    networks:
      - cansell-network

  # Redpanda for event streaming
  redpanda:
    image: redpandadata/redpanda:v23.2.14
    container_name: cansell-redpanda
    command:
      - redpanda start
      - --smp 1
      - --overprovisioned
      - --kafka-addr internal://0.0.0.0:9092,external://0.0.0.0:19092
      - --advertise-kafka-addr internal://redpanda:9092,external://localhost:19092
      - --pandaproxy-addr internal://0.0.0.0:8082,external://0.0.0.0:18082
      - --advertise-pandaproxy-addr internal://redpanda:8082,external://localhost:18082
      - --schema-registry-addr internal://0.0.0.0:8081,external://0.0.0.0:18081
      - --rpc-addr redpanda:33145
      - --advertise-rpc-addr redpanda:33145
    ports:
      - "18081:8081"  # Schema Registry
      - "18082:8082"  # Pandaproxy
      - "19092:9092"  # Kafka API
      - "19644:9644"  # Admin API
    volumes:
      - redpanda_data:/var/lib/redpanda/data
    networks:
      - cansell-network
    healthcheck:
      test: ["CMD-SHELL", "rpk cluster health | grep -q 'Healthy'"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redpanda Console for Kafka management
  redpanda-console:
    image: redpandadata/console:v2.3.1
    container_name: cansell-console
    environment:
      CONFIG_FILEPATH: /tmp/config.yml
      KAFKA_BROKERS: redpanda:9092
      KAFKA_SCHEMAREGISTRY_ENABLED: true
      KAFKA_SCHEMAREGISTRY_URLS: http://redpanda:8081
    ports:
      - "8080:8080"
    volumes:
      - ./infra/config/redpanda-console.yml:/tmp/config.yml
    depends_on:
      redpanda:
        condition: service_healthy
    networks:
      - cansell-network

  # MinIO for S3-compatible object storage (development)
  minio:
    image: minio/minio:latest
    container_name: cansell-minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - cansell-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Mailhog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: cansell-mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - cansell-network

volumes:
  postgres_data:
  mongodb_data:
  redis_data:
  opensearch_data:
  redpanda_data:
  minio_data:

networks:
  cansell-network:
    driver: bridge
