{"name": "@can-sell/config", "version": "0.1.0", "private": true, "scripts": {"build": "echo 'No build needed for config package'", "dev": "echo 'No dev needed for config package'", "lint": "eslint .", "type-check": "echo 'No type-check needed for config package'"}, "dependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-jsx-a11y": "^6.7.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.0.0", "tailwindcss": "^3.3.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "exports": {"./eslint": "./eslint.js", "./prettier": "./prettier.js", "./tailwind": "./tailwind.js", "./typescript": "./tsconfig.json"}}