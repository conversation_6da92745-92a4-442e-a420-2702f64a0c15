# Backend Development Dockerfile
FROM node:18-alpine

# Install pnpm
RUN npm install -g pnpm

# Set working directory
WORKDIR /app

# Copy root package.json and pnpm files
COPY package.json pnpm-lock.yaml* pnpm-workspace.yaml ./
COPY turbo.json ./

# Copy packages
COPY packages ./packages

# Copy backend app
COPY apps/backend ./apps/backend

# Install dependencies
RUN pnpm install

# Build shared packages
RUN pnpm build --filter="@can-sell/types"
RUN pnpm build --filter="@can-sell/utils"

WORKDIR /app/apps/backend

EXPOSE 3001
EXPOSE 9229

CMD ["pnpm", "start:debug"]
