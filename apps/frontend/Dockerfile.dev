# Frontend Development Dockerfile
FROM node:18-alpine

# Install pnpm
RUN npm install -g pnpm

# Set working directory
WORKDIR /app

# Copy root package.json and pnpm files
COPY package.json pnpm-lock.yaml* pnpm-workspace.yaml ./
COPY turbo.json ./

# Copy packages
COPY packages ./packages

# Copy frontend app
COPY apps/frontend ./apps/frontend

# Install dependencies
RUN pnpm install

# Build shared packages
RUN pnpm build --filter="@can-sell/types"
RUN pnpm build --filter="@can-sell/utils"
RUN pnpm build --filter="@can-sell/config"

WORKDIR /app/apps/frontend

EXPOSE 3000

CMD ["pnpm", "dev"]
