#!/bin/bash

# CanSell Ultimate Docker Runner Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to check if Docker Compose is available
check_docker_compose() {
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
    print_success "Docker Compose is available"
}

# Function to create environment file if it doesn't exist
setup_env() {
    if [ ! -f .env ]; then
        print_status "Creating .env file from template..."
        cp .env.example .env
        print_warning "Please update .env file with your configuration before running in production"
    else
        print_success ".env file already exists"
    fi
}

# Function to make scripts executable
setup_scripts() {
    print_status "Making scripts executable..."
    chmod +x infra/scripts/*.sh
    chmod +x scripts/*.sh
    print_success "Scripts are now executable"
}

# Function to run production environment
run_production() {
    print_status "Starting CanSell Ultimate in production mode..."
    
    # Build and start services
    docker-compose down --remove-orphans
    docker-compose build --no-cache
    docker-compose up -d
    
    print_success "Production environment started!"
    print_status "Services are starting up. This may take a few minutes..."
    
    # Wait for services to be healthy
    print_status "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    check_services
}

# Function to run development environment
run_development() {
    print_status "Starting CanSell Ultimate in development mode..."
    
    # Build and start services
    docker-compose -f docker-compose.dev.yml down --remove-orphans
    docker-compose -f docker-compose.dev.yml build --no-cache
    docker-compose -f docker-compose.dev.yml up -d
    
    print_success "Development environment started!"
    print_status "Services are starting up. This may take a few minutes..."
    
    # Wait for services to be healthy
    print_status "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    check_services_dev
}

# Function to run only infrastructure services
run_infrastructure() {
    print_status "Starting infrastructure services only..."
    
    docker-compose -f docker-compose.dev.yml up -d postgres mongodb redis opensearch redpanda minio mailhog opensearch-dashboards redpanda-console
    
    print_success "Infrastructure services started!"
    print_status "You can now run the applications locally with 'pnpm dev'"
}

# Function to check service health
check_services() {
    print_status "Checking service health..."
    
    services=("frontend" "backend" "postgres" "mongodb" "redis" "opensearch" "redpanda")
    for service in "${services[@]}"; do
        if docker-compose ps | grep -q "${service}.*Up"; then
            print_success "$service is running"
        else
            print_warning "$service is not running properly"
        fi
    done
}

# Function to check development service health
check_services_dev() {
    print_status "Checking service health..."
    
    services=("frontend-dev" "backend-dev" "postgres" "mongodb" "redis" "opensearch" "redpanda")
    for service in "${services[@]}"; do
        if docker-compose -f docker-compose.dev.yml ps | grep -q "${service}.*Up"; then
            print_success "$service is running"
        else
            print_warning "$service is not running properly"
        fi
    done
}

# Function to show service URLs
show_urls() {
    echo ""
    print_success "🎯 CanSell Ultimate is ready!"
    echo ""
    echo "📱 Application URLs:"
    echo "   Frontend:              http://localhost:3000"
    echo "   Backend API:           http://localhost:3001"
    echo "   API Documentation:     http://localhost:3001/api/docs"
    echo ""
    echo "🔧 Management UIs:"
    echo "   OpenSearch Dashboards: http://localhost:5601"
    echo "   Redpanda Console:      http://localhost:8080"
    echo "   MinIO Console:         http://localhost:9001"
    echo "   MailHog:               http://localhost:8025"
    echo ""
    echo "📊 Database Connections:"
    echo "   PostgreSQL:            localhost:5432 (user: dev, password: dev123)"
    echo "   MongoDB:               localhost:27017 (user: dev, password: dev123)"
    echo "   Redis:                 localhost:6379 (password: dev123)"
    echo "   OpenSearch:            localhost:9200"
    echo ""
    echo "🚀 Happy coding!"
}

# Function to stop all services
stop_services() {
    print_status "Stopping all services..."
    docker-compose down --remove-orphans
    docker-compose -f docker-compose.dev.yml down --remove-orphans
    print_success "All services stopped"
}

# Function to clean up Docker resources
cleanup() {
    print_status "Cleaning up Docker resources..."
    docker-compose down --remove-orphans --volumes
    docker-compose -f docker-compose.dev.yml down --remove-orphans --volumes
    docker system prune -f
    print_success "Cleanup completed"
}

# Function to show logs
show_logs() {
    local service=${1:-""}
    if [ -n "$service" ]; then
        print_status "Showing logs for $service..."
        docker-compose logs -f "$service"
    else
        print_status "Showing logs for all services..."
        docker-compose logs -f
    fi
}

# Function to show help
show_help() {
    echo "CanSell Ultimate Docker Runner"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  prod, production     Start production environment"
    echo "  dev, development     Start development environment with hot reload"
    echo "  infra, infrastructure Start only infrastructure services"
    echo "  stop                 Stop all services"
    echo "  clean, cleanup       Stop services and clean up Docker resources"
    echo "  logs [service]       Show logs (optionally for specific service)"
    echo "  status               Check service status"
    echo "  help                 Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 dev               # Start development environment"
    echo "  $0 prod              # Start production environment"
    echo "  $0 infra             # Start only infrastructure"
    echo "  $0 logs backend      # Show backend logs"
    echo "  $0 stop              # Stop all services"
}

# Main script logic
main() {
    local command=${1:-"help"}
    
    echo "🚀 CanSell Ultimate Docker Runner"
    echo "=================================="
    
    case $command in
        "prod"|"production")
            check_docker
            check_docker_compose
            setup_env
            setup_scripts
            run_production
            show_urls
            ;;
        "dev"|"development")
            check_docker
            check_docker_compose
            setup_env
            setup_scripts
            run_development
            show_urls
            ;;
        "infra"|"infrastructure")
            check_docker
            check_docker_compose
            setup_env
            setup_scripts
            run_infrastructure
            echo ""
            print_success "Infrastructure services are ready!"
            echo "You can now run 'pnpm dev' to start the applications locally."
            ;;
        "stop")
            stop_services
            ;;
        "clean"|"cleanup")
            cleanup
            ;;
        "logs")
            show_logs "$2"
            ;;
        "status")
            check_services
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run the main function with all arguments
main "$@"
